package functions

import (
	"context"
	"fmt"

	"github.com/akuityio/akuity-platform/internal/utils/ai"
	"github.com/akuityio/akuity-platform/models/models"
	"github.com/samber/lo"
)

func (fc *Controller) addRunbookFunctions() error {
	if err := addFunction(fc, Function{
		Tool: ai.Tool{
			Name:        "get-runbooks-by-names",
			Description: `Retrieves runbooks by their names.`,
		},
		DisplayName: "Get Runbooks by Names",
	}, fc.getRunbooksByNames); err != nil {
		return err
	}

	return nil
}

func (fc *Controller) getRunbooksByNames(ctx context.Context, args struct {
	Names []string `json:"names" description:"Array of runbook names to retrieve" required:"true"`
}) (string, error) {
	// Get conversation to extract instance ID
	conv := GetConversation(ctx)
	if conv == nil {
		return "", fmt.Errorf("context has no conversation")
	}
	if conv.InstanceID.IsZero() {
		return "", fmt.Errorf("runbooks can only be retrieved for conversations related to ArgoCD instances")
	}

	instanceID := conv.InstanceID.String

	// Validate required fields
	var runbooks []models.Runbook
	if len(args.Names) == 0 {
		if err := conv.Runbooks.Unmarshal(&runbooks); err != nil {
			return "", &InvalidArgsError{Message: "Runbooks data is invalid or empty"}
		} else if len(runbooks) == 0 {
			return "", &InvalidArgsError{Message: "At least one runbook name is required"}
		}
	}

	// Get the instance configuration directly from the repo
	instanceConfig, err := fc.RepoSet.ArgoCDInstanceConfigs().GetByID(ctx, instanceID)
	if err != nil {
		return "", fmt.Errorf("failed to get instance configuration: %w", err)
	}

	// Get current spec
	spec, err := instanceConfig.GetSpec()
	if err != nil {
		return "", fmt.Errorf("failed to get instance spec: %w", err)
	}

	// Determine target runbook names
	var targetNames []string
	if len(args.Names) == 0 {
		// If no names provided, get from conversation runbooks
		targetNames = lo.Map(runbooks, func(item models.Runbook, index int) string {
			return item.Name
		})
	} else {
		targetNames = args.Names
	}

	// Filter runbooks by target names using lo functions
	foundRunbooks := lo.Filter(spec.KubeVisionConfig.AIConfig.Runbooks, func(item models.Runbook, index int) bool {
		return lo.Contains(targetNames, item.Name)
	})

	if len(foundRunbooks) == 0 {
		return "No runbooks found with the specified names", nil
	}

	// Format the response
	runbookMsg := `
	The following are the runbooks that you can use to resolve the incident:
	`
	for _, runbook := range foundRunbooks {
		runbookMsg += fmt.Sprintf(`Runbook: "%s", Steps: %s \n
		`, runbook.Name, runbook.Content)
	}
	return runbookMsg, nil
}
