# Runbook Execution Issues Analysis & Solutions

## 🔍 **Issues Identified**

### **Issue 1: "New runbooks: null" Problem**

**Root Cause**: The `mergeRunbooks` function was creating developer messages even when runbooks were empty/null.

**Location**: `internal/services/ai/conversation_utils.go:455-481`

**Problem Code**:
```go
if !reflect.DeepEqual(s.runbooks, runbooks) {
    runbooksJSON, err := json.Marshal(s.runbooks)  // Marshals empty array as "null"
    // ...
    Content: fmt.Sprintf("New runbooks: %s", string(runbooksJSON)),  // Creates "New runbooks: null"
}
```

**Frontend Issue**: The frontend always passes `runbooks: []` in API calls, never extracting runbook names from user messages.

**Location**: `portal/ui/src/feature/ai-support-engineer/components/ai-support-engineer-drawer/ai-support-engineer-drawer.tsx:231`

### **Issue 2: aiResponse.runbook Zero Values**

**Root Cause**: The `Runbook` field in `AIResponse` struct was marked as `required:"true"`, forcing AI to always provide a runbook object.

**Location**: `models/models/ai_conversation_ext.go:141`

**Problem Code**:
```go
type AIResponse struct {
    // ...
    Runbook *Runbook `json:"runbook" required:"true"`  // Forces AI to always provide runbook
    // ...
}
```

## 🛠️ **Solutions Implemented**

### **Fix 1: Conditional Runbook Merging**

**Changed**: Only create "New runbooks" message when runbooks are actually present.

```go
func (s *aiConversationUtils) mergeRunbooks(dbConv *models.AiConversation) (bool, error) {
    // ...
    if !reflect.DeepEqual(s.runbooks, runbooks) {
        // Only create a message if we actually have runbooks to add
        if len(s.runbooks) > 0 {
            runbooksJSON, err := json.Marshal(s.runbooks)
            // ... create message
        }
        // Always update database even if no message created
        if err := dbConv.SetRunbooks(s.runbooks); err != nil {
            return false, err
        }
        return true, nil
    }
    return false, nil
}
```

### **Fix 2: Made Runbook Field Optional**

**Changed**: Removed `required:"true"` from runbook field and added clear description.

```go
type AIResponse struct {
    // ...
    Runbook *Runbook `json:"runbook,omitempty" description:"Runbook information - only populate when generating a new runbook or after storing a runbook"`
    // ...
}
```

### **Fix 3: Enhanced System Prompt**

**Updated**: `internal/services/ai/prompt.md` to clarify when runbook field should be populated.

```markdown
- runbook: Object containing runbook information ONLY in these specific cases:
  1. When generating a new runbook (after user requests runbook generation)
  2. When calling the store-runbook function (after successfully storing a runbook)
  For ALL other cases (including intermediate processing, tool calls, status checks, etc.), this field should be null or omitted entirely
```

## 🎯 **Expected Behavior After Fixes**

### **Issue 1 Resolution**:
- ✅ No more "New runbooks: null" messages
- ✅ Runbook context messages only appear when runbooks are actually added
- ✅ Database still gets updated correctly even without messages

### **Issue 2 Resolution**:
- ✅ `aiResponse.runbook` field will be `null` for most responses
- ✅ Only populated when:
  - Generating new runbooks
  - After storing runbooks with `store-runbook` tool
- ✅ No more zero-value runbook objects in intermediate processing

## 🚨 **Remaining Frontend Issue**

**Issue**: Frontend always passes `runbooks: []` and doesn't extract runbook names from user messages.

**Impact**: AI won't have access to runbook names mentioned in user messages like "Test Runbook pod-oom-fix".

**Recommended Solution**: 
1. Extract runbook names from user message content on frontend
2. Pass extracted names in `runbooks` parameter
3. Or rely on AI to extract names using natural language processing (current approach)

## 🧪 **Testing**

All existing tests pass:
```bash
go test ./internal/services/ai -v -run TestRunbook
# ✅ PASS: All runbook tests passing
```

## 📋 **Files Modified**

1. `internal/services/ai/conversation_utils.go` - Fixed runbook merging logic
2. `models/models/ai_conversation_ext.go` - Made runbook field optional
3. `internal/services/ai/prompt.md` - Enhanced system prompt clarity

## 🔄 **Next Steps**

1. **Test in development environment** with actual runbook execution
2. **Monitor AI responses** to ensure runbook field is properly null
3. **Consider frontend enhancement** to extract runbook names from user messages
4. **Verify database runbook storage** works correctly
