package ai

import (
	"testing"
)

func TestIsValidResourceName(t *testing.T) {
	tests := []struct {
		name     string
		input    string
		expected bool
	}{
		// Valid cases
		{
			name:     "valid lowercase name",
			input:    "argocd-redis",
			expected: true,
		},
		{
			name:     "valid name with numbers",
			input:    "argocd-redis-123",
			expected: true,
		},
		{
			name:     "valid name with hyphens",
			input:    "argocd-redis-ha",
			expected: true,
		},
		{
			name:     "valid name with single character",
			input:    "a",
			expected: true,
		},
		{
			name:     "valid name with maximum length",
			input:    "a" + string(make([]byte, 252)) + "z",
			expected: false,
		},

		// Invalid cases
		{
			name:     "empty name",
			input:    "",
			expected: false,
		},
		{
			name:     "name starting with number",
			input:    "123-argocd-redis",
			expected: true,
		},
		{
			name:     "name starting with hyphen",
			input:    "-argocd-redis",
			expected: false,
		},
		{
			name:     "name ending with hyphen",
			input:    "argocd-redis-",
			expected: false,
		},
		{
			name:     "name with uppercase letters",
			input:    "ArgoCD-Redis",
			expected: false,
		},
		{
			name:     "name with special characters",
			input:    "argocd-redis@ha",
			expected: false,
		},
		{
			name:     "name with spaces",
			input:    "argocd redis",
			expected: false,
		},
		{
			name:     "name with consecutive hyphens",
			input:    "argocd--redis",
			expected: true,
		},
		{
			name:     "name exceeding maximum length",
			input:    "a" + string(make([]byte, 253)) + "z",
			expected: false,
		},
		{
			name:     "name with dots",
			input:    "argocd.redis",
			expected: true,
		},
		{
			name:     "name with underscores",
			input:    "argocd_redis",
			expected: false,
		},
		{
			name:     "name with wildcard",
			input:    "argocd-redis-*",
			expected: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := isValidResourceName(tt.input)
			if result != tt.expected {
				t.Errorf("isValidResourceName(%q) = %v, want %v", tt.input, result, tt.expected)
			}
		})
	}
}
