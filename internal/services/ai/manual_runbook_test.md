# Runbook Execution Feature

This document describes the implemented runbook execution feature and how to use it.

## Implementation Summary

We have successfully implemented runbook execution by modifying the existing `CreateMessage` API to automatically detect "Test Runbook" messages and use the appropriate message role to trigger AI workflow.

## How It Works

1. **API Endpoint**: Use the existing `CreateAIMessage` API
2. **Message Detection**: When content starts with "Test Runbook", the message uses `developer` role
3. **AI Processing**: AI automatically follows the runbook execution workflow from system prompt
4. **Tool Calling**: AI calls `get-runbooks-by-names` tool automatically

## Usage

### API Call Example
```http
POST /api/v1/orgs/{organization_id}/ai/conversations/{conversation_id}/messages
Content-Type: application/json

{
  "content": "Test Runbook pod-oom-fix service-selector-mismatch",
  "organization_id": "org-123",
  "instance_id": "instance-456"
}
```

### Frontend Usage
Users can simply type in the chat:
```
Test Runbook pod-oom-fix service-selector-mismatch
```

## Expected Behavior

Based on our code analysis:

### 1. Message Creation
```go
// Our ExecuteRunbook method creates:
msg := ai.Message{
    Role:    ai.MessageRoleDeveloper,
    Content: "Test Runbook pod-oom-fix service-selector-mismatch",
}
```

### 2. AI Processing
According to `prompt.md` lines 158-183, when AI sees "Test Runbook", it should:

1. **Extract runbook names**: Parse the message to identify runbook names
2. **Retrieve runbooks**: Use `get-runbooks-by-names` tool
3. **Analyze symptoms**: Examine the **Symptoms** section in runbook content
4. **Match to current context**: Compare symptoms with current application context
5. **Select appropriate runbooks**: Choose runbooks that match current problems
6. **Execute remediation**: Apply the selected runbooks' solutions

### 3. Tool Call Verification
The AI should automatically call:
```json
{
  "name": "get-runbooks-by-names",
  "arguments": {
    "names": ["pod-oom-fix", "service-selector-mismatch"]
  }
}
```

## Test Steps

1. **Create a conversation** with ArgoCD application context
2. **Call ExecuteRunbook method** with runbook names
3. **Trigger ProcessConversation** to process the message
4. **Verify AI response** contains tool calls to `get-runbooks-by-names`
5. **Check workflow execution** follows the expected steps

## Expected Results

- ✅ Message created with correct format
- ✅ Message marked as unprocessed (`Processed = false`)
- ✅ ProcessConversation detects unprocessed message
- ✅ AI calls `get-runbooks-by-names` tool automatically
- ✅ AI follows the runbook execution workflow from prompt.md

## Verification Points

1. **Message Format**: "Test Runbook [space-separated-runbook-names]"
2. **Tool Call**: AI should call `get-runbooks-by-names` with correct arguments
3. **Workflow**: AI should follow the 6-step workflow from the prompt
4. **Error Handling**: If no runbooks found, AI should inform user appropriately

## Next Steps

To fully test this:
1. Set up a test environment with database and AI client
2. Create a conversation with ArgoCD context
3. Add some test runbooks to the instance configuration
4. Call our ExecuteRunbook method
5. Verify the AI response and tool calls
