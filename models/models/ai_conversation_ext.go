package models

import (
	"encoding/json"
	"fmt"
	"time"

	"github.com/sasha<PERSON>nov/go-openai/jsonschema"
	"github.com/volatiletech/null/v8"

	"github.com/akuityio/akuity-platform/internal/utils/ai"
)

func (t *AiConversation) GetID() string {
	return t.ID
}

func (t *AiConversation) SetID(id string) {
	t.ID = id
}

type ArgoCDApplicationConversationContext struct {
	// TODO: We need to add Namespace to the context to make it compatible with the app in any namespace feature
	InstanceID string `json:"instanceId" description:"Argo CD Instance ID" required:"true"`
	Name       string `json:"name" description:"Argo CD Application Name" required:"true"`
}

type K8SNamespaceConversationContext struct {
	InstanceID string `json:"instanceId" description:"Argo CD Instance ID" required:"true"`
	ClusterID  string `json:"clusterId" description:"Cluster ID" required:"true"`
	Name       string `json:"name" description:"Namespace name" required:"true"`
}

type KargoProjectConversationContext struct {
	InstanceID string `json:"instanceId" description:"Kargo Instance ID" required:"true"`
	Name       string `json:"name" description:"Kargo Project Name" required:"true"`
}

type ConversationContext struct {
	ArgoCDApp    *ArgoCDApplicationConversationContext `json:"argoCDApp,omitempty" required:"true"`
	K8SNamespace *K8SNamespaceConversationContext      `json:"k8sNamespace,omitempty" required:"true"`
	KargoProject *KargoProjectConversationContext      `json:"kargoProject,omitempty" required:"true"`
}

type AIConversationUser struct {
	ID   string `json:"id,omitempty"`
	Type string `json:"type,omitempty"`
}

type AIConversationMessage struct {
	ID                string              `json:"id,omitempty"`
	Message           ai.Message          `json:"message,omitempty"`
	CreationTimestamp time.Time           `json:"creationTimestamp,omitempty"`
	AIResponse        AIResponse          `json:"aiResponse,omitempty"`
	IsUseful          *bool               `json:"isUseful,omitempty"`
	Processed         bool                `json:"processed,omitempty"`
	ToolError         string              `json:"toolError,omitempty"`
	User              *AIConversationUser `json:"user,omitempty"`
	ToolCallSummary   *string             `json:"toolCallSummary,omitempty"`
}

func (m *AIConversationMessage) GetUser(akUserById map[string]string) string {
	if m.User == nil {
		return ""
	}

	if m.User.Type == "user" {
		if fullName, ok := akUserById[m.User.ID]; ok {
			return fullName
		} else {
			return m.User.ID
		}
	}
	return fmt.Sprintf("%s (%s)", m.User.ID, m.User.Type)
}

func (t *AiConversation) GetContexts() ([]ConversationContext, error) {
	contexts := []ConversationContext{}
	if t.Contexts.IsZero() {
		return contexts, nil
	}
	if err := json.Unmarshal(t.Contexts.JSON, &contexts); err != nil {
		return nil, err
	}
	return contexts, nil
}

func (t *AiConversation) SetContexts(contexts []ConversationContext) error {
	data, err := json.Marshal(contexts)
	if err != nil {
		return err
	}
	t.Contexts = null.JSONFrom(data)
	return nil
}

func (t *AiConversation) GetMessages() ([]*AIConversationMessage, error) {
	messages := []*AIConversationMessage{}
	if t.Messages.IsZero() {
		return messages, nil
	}
	if err := json.Unmarshal(t.Messages.JSON, &messages); err != nil {
		return nil, err
	}
	return messages, nil
}

func (t *AiConversation) SetMessages(messages []*AIConversationMessage) error {
	data, err := json.Marshal(messages)
	if err != nil {
		return err
	}
	t.Messages = null.JSONFrom(data)
	return nil
}

func (t *AiConversation) GetMetadata() (*AIConversationMetadata, error) {
	metadata := &AIConversationMetadata{}
	if t.Metadata.IsZero() {
		return metadata, nil
	}
	if err := json.Unmarshal(t.Metadata.JSON, metadata); err != nil {
		return nil, err
	}
	return metadata, nil
}

func (t *AiConversation) SetMetadata(metadata *AIConversationMetadata) error {
	data, err := json.Marshal(metadata)
	if err != nil {
		return err
	}
	t.Metadata = null.JSONFrom(data)
	return nil
}

type AIResponse struct {
	Content           string                `json:"content,omitempty" description:"Response message to the user" required:"true"`
	NeedToolRun       bool                  `json:"needToolRun,omitempty" description:"Boolean indicating that a system tool should be right after this message" required:"true"`
	ThinkingProcess   string                `json:"thinkingProcess,omitempty" description:"Step-by-step analysis process" required:"true"`
	Runbook           *Runbook              `json:"runbook,omitempty" required:"true"`
	SuggestedContexts []ConversationContext `json:"suggestedContexts,omitempty" description:"Suggested contexts to be added to the conversation" required:"true"`
	SuggestedChanges  []SuggestedChange     `json:"suggestedChanges,omitempty" description:"Suggested changes after investigating the resource manifests" required:"true"`
}

type SuggestedChange struct {
	Context ConversationContext `json:"context,omitempty" required:"true"`
	Old     string              `json:"old,omitempty" description:"Current resource manifest" required:"true"`
	Patch   string              `json:"patch,omitempty" description:"Suggested merge patch in JSON format (RFC 7386)" required:"true"`
	New     string              `json:"new,omitempty" description:"New manifest after applying the merge patch" required:"true"`
	Applied bool                `json:"applied,omitempty" description:"Boolean indicating if the patch is already applied or not" required:"true"`
}

type AiTask struct {
	ID          string `json:"id,omitempty" required:"true"`
	Description string `json:"description,omitempty" required:"true"`
	Timestamp   time.Time
}

type Incident struct {
	Runbooks   []string   `json:"runbooks,omitempty" description:"List of runbooks to be executed"`
	ResolvedAt *time.Time `json:"resolvedAt,omitempty" description:"Time when the incident was resolved"`

	// Argo CD Instance ID related to the incident. Used to find runbooks
	InstanceID string `json:"instanceId,omitempty"`

	// Argo CD Application name related to the incident
	Application string `json:"application,omitempty" description:"Name of the application"`

	// ClusterID and Namespace related to the incident
	ClusterID string `json:"clusterId,omitempty"`
	Namespace string `json:"namespace,omitempty"`

	Summary    string `json:"summary,omitempty" description:"The incident summary"`
	RootCause  string `json:"rootCause,omitempty" description:"Description of the incident root cause"`
	Resolution string `json:"resolution,omitempty" description:"Description of the incident resolution"`
}

func (i *Incident) Target() string {
	if i.Application != "" {
		return fmt.Sprintf("Argo CD Application: %s", i.Application)
	}
	return fmt.Sprintf("K8s Namespace: %s", i.Namespace)
}

type AIConversationMetadata struct {
	// LastProcessTime is the last time the AI processed the conversation
	LastProcessTime time.Time `json:"lastProcessTime,omitempty"`
	// Tasks contains list of tasks that are due to run
	Tasks []AiTask `json:"tasks,omitempty"`
	// Incident contains incident metadata
	Incident *Incident `json:"incident,omitempty"`
	// PromotionAnalysis contains Kargo promotion analysis data
	PromotionAnalysis *PromotionAnalysis `json:"promotionAnalysis,omitempty"`
	// ProcessingError contains processing error if any
	ProcessingError string `json:"processingError,omitempty"`
	// Usage contains AI usage statistics for the conversation
	Usage AIUsage `json:"usage,omitempty"`
}

type PromotionAnalysis struct {
	CompletedAt *time.Time `json:"completedAt,omitempty" description:"Time when the promotion analysis was completed"`
	InstanceID  string     `json:"instanceId,omitempty" description:"Kargo instance ID"`
	// Kargo project and promotion details
	Project        string `json:"project,omitempty" description:"Name of the Kargo project"`
	Freight        string `json:"freight,omitempty" description:"Freight being promoted"`
	Stage          string `json:"stage,omitempty" description:"Destination stage"`
	CurrentFreight string `json:"currentFreight,omitempty" description:"Current freight"`

	Summary       string `json:"summary,omitempty" description:"The promotion analysis summary"`
	RiskLevel     string `json:"riskLevel,omitempty" description:"Overall risk assessment (low, medium, high)"`
	Decision      string `json:"decision,omitempty" description:"Final promotion decision or recommendation"`
	CommitDiffURL string `json:"commitDiffUrl,omitempty" description:"GitHub compare URL for the commit differences"`
}

func GetAIResponseParametersSchema() (*jsonschema.Definition, error) {
	schema, err := jsonschema.GenerateSchemaForType(AIResponse{})
	if err != nil {
		return nil, err
	}
	return schema, nil
}

func (t *AiConversation) GetFeedbacks() ([]string, error) {
	var feedbacks []string
	if t.Feedbacks.IsZero() {
		return feedbacks, nil
	}
	if err := json.Unmarshal(t.Feedbacks.JSON, &feedbacks); err != nil {
		return nil, err
	}
	return feedbacks, nil
}

func (t *AiConversation) AppendFeedbacks(feedback string) error {
	feedbacks, err := t.GetFeedbacks()
	if err != nil {
		return err
	}
	feedbacks = append(feedbacks, feedback)
	feedbacksJSON, err := json.Marshal(feedbacks)
	if err != nil {
		return err
	}
	t.Feedbacks = null.JSONFrom(feedbacksJSON)
	return nil
}

func (t *AiConversation) GetRunbooks() ([]string, error) {
	var runbooks []string
	if t.Runbooks.IsZero() {
		return runbooks, nil
	}
	if err := json.Unmarshal(t.Runbooks.JSON, &runbooks); err != nil {
		return nil, err
	}
	return runbooks, nil
}

func (t *AiConversation) SetRunbooks(runbooks []string) error {
	data, err := json.Marshal(runbooks)
	if err != nil {
		return err
	}
	t.Runbooks = null.JSONFrom(data)
	return nil
}
